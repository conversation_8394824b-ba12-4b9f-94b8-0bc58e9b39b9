import { ProxyOAuthServerProvider } from '@modelcontextprotocol/sdk/server/auth/providers/proxyProvider.js';
import { OAuthClientInformationFull } from '@modelcontextprotocol/sdk/shared/auth.js';
import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';
import { OAuth2Config } from './oauth-config.js';

/**
 * Create a direct OIDC provider using only MCP SDK ProxyOAuthServerProvider
 * This eliminates the need for any custom provider class
 */
export function createOIDCProvider(config: OAuth2Config): ProxyOAuthServerProvider {
  console.error('🔧 Creating direct OIDC provider (no custom class needed):');
  console.error(`   OIDC Issuer: ${config.keycloak.issuer}`);
  console.error(`   Authorization URL: ${config.keycloak.authUrl}`);
  console.error(`   Token URL: ${config.keycloak.tokenUrl}`);
  console.error(`   UserInfo URL: ${config.keycloak.userInfoUrl}`);

  // Create token verification function
  const verifyAccessToken = async (token: string): Promise<AuthInfo> => {
    try {
      // Use OIDC token introspection endpoint (RFC 7662)
      const introspectionUrl = `${config.keycloak.issuer}/protocol/openid-connect/token/introspect`;
      
      const response = await fetch(introspectionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${Buffer.from(`${config.client.id}:${config.client.secret}`).toString('base64')}`,
        },
        body: new URLSearchParams({
          token,
          token_type_hint: 'access_token',
        }),
      });

      if (!response.ok) {
        throw new Error(`Token introspection failed: ${response.status}`);
      }

      const introspectionData = await response.json() as any;
      
      if (!introspectionData.active) {
        throw new Error('Token is not active');
      }

      // Get additional user info from userinfo endpoint
      const userInfoResponse = await fetch(config.keycloak.userInfoUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        },
      });

      const userInfo = userInfoResponse.ok ? await userInfoResponse.json() : null;

      return {
        token,
        clientId: introspectionData.client_id || introspectionData.azp,
        scopes: introspectionData.scope ? introspectionData.scope.split(' ') : [],
        expiresAt: introspectionData.exp,
        extra: {
          sub: introspectionData.sub,
          iat: introspectionData.iat,
          userInfo,
          introspectionData,
        },
      };
    } catch (error) {
      console.error('Token verification failed:', error);
      throw new Error(`Invalid access token: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Create client information function
  const getClient = async (clientId: string): Promise<OAuthClientInformationFull | undefined> => {
    // Return the configured client if it matches
    if (clientId === config.client.id) {
      return {
        client_id: config.client.id,
        client_secret: config.client.secret,
        redirect_uris: [config.client.redirectUri],
        grant_types: ['authorization_code', 'refresh_token'],
        response_types: ['code'],
        scope: config.client.scopes.join(' '),
        token_endpoint_auth_method: 'client_secret_basic',
      };
    }
    
    return undefined;
  };

  // Create and return the MCP SDK ProxyOAuthServerProvider directly
  const provider = new ProxyOAuthServerProvider({
    endpoints: {
      authorizationUrl: config.keycloak.authUrl,
      tokenUrl: config.keycloak.tokenUrl,
      revocationUrl: config.keycloak.revocationUrl,
      registrationUrl: config.keycloak.registrationUrl,
    },
    verifyAccessToken,
    getClient,
    fetch: fetch,
  });

  // Let OIDC server handle PKCE validation
  provider.skipLocalPkceValidation = true;

  console.error('✅ Direct OIDC provider created using MCP SDK ProxyOAuthServerProvider');
  
  return provider;
}

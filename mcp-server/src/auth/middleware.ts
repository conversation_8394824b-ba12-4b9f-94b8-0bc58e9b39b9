import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON>e<PERSON>Handler } from 'express';
import { requireBearerAuth } from '@modelcontextprotocol/sdk/server/auth/middleware/bearerAuth.js';
import { ProxyOAuthServerProvider } from '@modelcontextprotocol/sdk/server/auth/providers/proxyProvider.js';

/**
 * Create OAuth2 authentication middleware
 */
export function createOAuth2Middleware(
  provider: ProxyOAuthServerProvider,
  options: {
    requiredScopes?: string[];
    resourceMetadataUrl?: string;
  } = {}
): RequestHandler {
  return requireBearerAuth({
    verifier: provider,
    requiredScopes: options.requiredScopes,
    resourceMetadataUrl: options.resourceMetadataUrl,
  });
}

// Note: Custom middleware functions removed - using MCP SDK's requireBearerAuth instead
// The MCP SDK provides standardized authentication middleware that handles:
// - Bearer token validation
// - Proper WWW-Authenticate headers
// - OAuth2 error responses
// - Resource metadata integration



/**
 * Middleware to validate required scopes
 */
export function requireScopes(requiredScopes: string[]): Request<PERSON><PERSON>ler {
  return (req, res, next) => {
    const auth = (req as any).auth;
    
    if (!auth) {
      return res.status(401).json({
        error: 'unauthorized',
        error_description: 'Authentication required',
      });
    }

    const userScopes = auth.scopes || [];
    const hasRequiredScopes = requiredScopes.every(scope => 
      userScopes.includes(scope)
    );

    if (!hasRequiredScopes) {
      return res.status(403).json({
        error: 'insufficient_scope',
        error_description: `Required scopes: ${requiredScopes.join(', ')}`,
        scope: requiredScopes.join(' '),
      });
    }

    next();
  };
}

/**
 * Middleware to validate user roles (Keycloak-specific)
 */
export function requireRoles(requiredRoles: string[], clientId?: string): RequestHandler {
  return (req, res, next) => {
    const auth = (req as any).auth;
    
    if (!auth || !auth.userInfo) {
      return res.status(401).json({
        error: 'unauthorized',
        error_description: 'Authentication required',
      });
    }

    const userInfo = auth.userInfo;
    let userRoles: string[] = [];

    // Get roles from realm_access
    if (userInfo.realm_access?.roles) {
      userRoles = userRoles.concat(userInfo.realm_access.roles);
    }

    // Get roles from resource_access (client-specific roles)
    if (clientId && userInfo.resource_access?.[clientId]?.roles) {
      userRoles = userRoles.concat(userInfo.resource_access[clientId].roles);
    }

    const hasRequiredRoles = requiredRoles.every(role => 
      userRoles.includes(role)
    );

    if (!hasRequiredRoles) {
      return res.status(403).json({
        error: 'insufficient_privileges',
        error_description: `Required roles: ${requiredRoles.join(', ')}`,
      });
    }

    next();
  };
}

/**
 * Error handling middleware for OAuth2 errors (MCP compliant)
 */
export function oAuth2ErrorHandler(serverBaseUrl: string): ErrorRequestHandler {
  return (err: any, _req: any, res: any, next: any) => {
    if (err.name === 'UnauthorizedError' || err.status === 401) {
      // MCP spec requires WWW-Authenticate header with resource metadata URL (RFC9728)
      res.set('WWW-Authenticate', `Bearer realm="${serverBaseUrl}", resource_metadata="${serverBaseUrl}/.well-known/oauth-protected-resource"`);
      return res.status(401).json({
        error: 'invalid_token',
        error_description: err.message || 'The access token provided is invalid',
      });
    }

    if (err.name === 'ForbiddenError' || err.status === 403) {
      return res.status(403).json({
        error: 'insufficient_scope',
        error_description: err.message || 'The request requires higher privileges than provided by the access token',
      });
    }

    // Pass other errors to the default error handler
    next(err);
  };
}

/**
 * Middleware to add CORS headers for OAuth2 endpoints
 */
export function oAuth2CorsMiddleware(): RequestHandler {
  return (req, res, next) => {
    // Allow CORS for OAuth2 endpoints
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    
    if (req.method === 'OPTIONS') {
      return res.sendStatus(200);
    }
    
    next();
  };
}

/**
 * Middleware to log OAuth2 requests
 */
export function oAuth2LoggingMiddleware(): RequestHandler {
  return (req, _res, next) => {
    const timestamp = new Date().toISOString();
    const auth = (req as any).auth;
    
    console.error(`🔐 [OAuth2] ${timestamp} ${req.method} ${req.path}`, {
      userAgent: req.headers['user-agent'],
      clientId: auth?.clientId,
      sub: auth?.sub,
      scopes: auth?.scopes,
    });
    
    next();
  };
}
